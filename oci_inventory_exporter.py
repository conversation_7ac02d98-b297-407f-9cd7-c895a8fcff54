# oci_inventory_exporter.py
#
# Description:
# This script connects to an OCI tenancy, scans all compartments for key
# resources, and exports the data into a two-sheet Excel file:
# 1. Master_Inventory: A filterable list of all core resources (Compute, OKE, DBs, LBs).
# 2. Network_Layout: A reference sheet for VCNs and Subnets.
#
# It relies on an 'Environment' tag for categorization.
#
# NEW: Backup Status Tracking
# - For VMs: Checks boot volume backup policies and retention settings
# - For MySQL DBs: Checks automatic backup configuration and retention
# - Adds columns: Backup Status, Backup Policy, Backup Retention
#
# Prerequisites:
# 1. Python 3.6+
# 2. OCI SDK for Python: pip install oci
# 3. Pandas & OpenPyXL for Excel export: pip install pandas openpyxl
# 4. OCI Configuration: A valid OCI config file must exist at ~/.oci/config

import oci
import pandas as pd
import logging
from oci.config import from_file

# --- Configuration ---
CONFIG_PROFILE = "INVENTORY_READONLY"
OUTPUT_FILE_NAME = "oci_resource_inventory.xlsx"
ENVIRONMENT_TAG_KEY = "Environment"
PURPOSE_TAG_KEY = "Purpose"
# --- End Configuration ---

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def get_all_compartments(identity_client, tenancy_id):
    """Fetches all active compartments in the tenancy."""
    logging.info("Fetching all compartments...")
    try:
        compartments = oci.pagination.list_call_get_all_results(
            identity_client.list_compartments,
            tenancy_id,
            compartment_id_in_subtree=True,
            access_level="ANY",
            lifecycle_state=oci.identity.models.Compartment.LIFECYCLE_STATE_ACTIVE
        ).data
        logging.info(f"Found {len(compartments)} active compartments.")
        return compartments
    except oci.exceptions.ServiceError as e:
        logging.error(f"Failed to fetch compartments: {e}")
        return []


def get_subscribed_regions(identity_client, tenancy_id):
    """Fetches all regions the tenancy is subscribed to."""
    logging.info("Fetching subscribed regions...")
    try:
        regions = identity_client.list_region_subscriptions(tenancy_id).data
        return [region.region_name for region in regions]
    except oci.exceptions.ServiceError as e:
        logging.error(f"Failed to fetch subscribed regions: {e}")
        return []


def get_resource_tags(resource):
    """Extracts both freeform and defined tags from a resource."""
    tags = {}
    if hasattr(resource, 'freeform_tags') and resource.freeform_tags:
        tags.update(resource.freeform_tags)
    if hasattr(resource, 'defined_tags') and resource.defined_tags:
        for namespace, tag_set in resource.defined_tags.items():
            for key, value in tag_set.items():
                tags[f"{namespace}.{key}"] = value
    return tags


def get_boot_volume_backup_status(compute_client, blockstorage_client, instance_id, compartment_id):
    """Gets backup status for the boot volume of a compute instance."""
    try:
        # Get boot volume attachments for the instance
        boot_volume_attachments = oci.pagination.list_call_get_all_results(
            compute_client.list_boot_volume_attachments,
            availability_domain=None,  # Will search all ADs
            compartment_id=compartment_id,
            instance_id=instance_id
        ).data

        if not boot_volume_attachments:
            return "No Boot Volume", "N/A", "N/A"

        boot_volume_id = boot_volume_attachments[0].boot_volume_id

        # Method 1: Try to get backup policy assignment directly
        try:
            # Try to get the backup policy assignment for this boot volume
            policy_assignments = oci.pagination.list_call_get_all_results(
                blockstorage_client.list_volume_backup_policy_assignments,
                compartment_id=compartment_id,
                asset_id=boot_volume_id
            ).data

            if policy_assignments:
                policy_assignment = policy_assignments[0]  # Take the first one

                # Get policy details
                try:
                    policy = blockstorage_client.get_volume_backup_policy(
                        policy_id=policy_assignment.policy_id
                    ).data

                    # Extract retention information from schedules
                    retention_days = "N/A"
                    if hasattr(policy, 'schedules') and policy.schedules:
                        # Get the maximum retention from all schedules
                        max_retention = 0
                        for schedule in policy.schedules:
                            if hasattr(schedule, 'retention_seconds') and schedule.retention_seconds:
                                retention_seconds = schedule.retention_seconds
                                retention_days_calc = retention_seconds // (24 * 3600)
                                max_retention = max(max_retention, retention_days_calc)
                        if max_retention > 0:
                            retention_days = f"{max_retention} days"

                    return "Enabled", policy.display_name, retention_days
                except oci.exceptions.ServiceError:
                    return "Enabled", "Policy Details Unavailable", "N/A"
            else:
                return "No Policy", "N/A", "N/A"

        except oci.exceptions.ServiceError as e:
            # Method 2: If list method fails, try the get method
            try:
                policy_assignment = blockstorage_client.get_volume_backup_policy_asset_assignment(
                    asset_id=boot_volume_id
                ).data

                if policy_assignment:
                    # Get policy details
                    try:
                        policy = blockstorage_client.get_volume_backup_policy(
                            policy_id=policy_assignment.policy_id
                        ).data

                        # Extract retention information from schedules
                        retention_days = "N/A"
                        if hasattr(policy, 'schedules') and policy.schedules:
                            # Get the maximum retention from all schedules
                            max_retention = 0
                            for schedule in policy.schedules:
                                if hasattr(schedule, 'retention_seconds') and schedule.retention_seconds:
                                    retention_seconds = schedule.retention_seconds
                                    retention_days_calc = retention_seconds // (24 * 3600)
                                    max_retention = max(max_retention, retention_days_calc)
                            if max_retention > 0:
                                retention_days = f"{max_retention} days"

                        return "Enabled", policy.display_name, retention_days
                    except oci.exceptions.ServiceError:
                        return "Enabled", "Policy Details Unavailable", "N/A"
                else:
                    return "No Policy", "N/A", "N/A"

            except oci.exceptions.ServiceError as e2:
                logging.warning(f"Could not get backup policy for boot volume {boot_volume_id}: {e2}")
                return "No Policy", "N/A", "N/A"

    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not get backup status for instance {instance_id}: {e}")
        return "Error", "N/A", "N/A"


def get_mysql_backup_status(db_system):
    """Gets backup status for a MySQL database system."""
    try:
        if hasattr(db_system, 'backup_policy') and db_system.backup_policy:
            backup_policy = db_system.backup_policy

            # Check if backup is enabled
            if hasattr(backup_policy, 'is_enabled') and backup_policy.is_enabled:
                # Get retention period
                retention = "N/A"
                if hasattr(backup_policy, 'retention_in_days') and backup_policy.retention_in_days:
                    retention = f"{backup_policy.retention_in_days} days"

                # Get backup window
                window = "N/A"
                if hasattr(backup_policy, 'window_start_time') and backup_policy.window_start_time:
                    window = backup_policy.window_start_time

                return "Enabled", "Automatic Backup", retention
            else:
                return "Disabled", "N/A", "N/A"
        else:
            return "No Policy", "N/A", "N/A"

    except Exception as e:
        logging.warning(f"Could not get backup status for database {db_system.display_name}: {e}")
        return "Error", "N/A", "N/A"


def process_compartment_resources(config, compartment, region_name):
    """Fetches key resources and their relevant details for a compartment and region."""
    all_resources = []
    config["region"] = region_name

    # Initialize clients
    compute_client = oci.core.ComputeClient(config)
    network_client = oci.core.VirtualNetworkClient(config)
    load_balancer_client = oci.load_balancer.LoadBalancerClient(config)
    oke_client = oci.container_engine.ContainerEngineClient(config)
    mysql_client = oci.mysql.DbSystemClient(config)
    blockstorage_client = oci.core.BlockstorageClient(config)

    # --- Pre-fetch network layout for mapping ---
    try:
        vcns_data = oci.pagination.list_call_get_all_results(network_client.list_vcns,
                                                             compartment_id=compartment.id).data
        subnets_data = oci.pagination.list_call_get_all_results(network_client.list_subnets,
                                                                compartment_id=compartment.id).data
        vcn_map = {v.id: v.display_name for v in vcns_data}
        subnet_map = {s.id: (s.display_name, vcn_map.get(s.vcn_id, "N/A")) for s in subnets_data}
    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not pre-fetch network layout in '{compartment.name}' ({region_name}): {e}")
        vcn_map, subnet_map = {}, {}

    # --- 1. VCNs and Subnets (for the Network_Layout sheet) ---
    for vcn in vcns_data:
        tags = get_resource_tags(vcn)
        all_resources.append({
            "Resource Type": "VCN", "Name": vcn.display_name, "CIDR Block": vcn.cidr_block,
            "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
            "Compartment ID": compartment.id, "Region": region_name
        })
    for subnet in subnets_data:
        tags = get_resource_tags(subnet)
        all_resources.append({
            "Resource Type": "Subnet", "Name": subnet.display_name, "CIDR Block": subnet.cidr_block,
            "VCN Name": vcn_map.get(subnet.vcn_id, "N/A"),
            "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
            "Compartment ID": compartment.id, "Region": region_name
        })

    # --- 2. Compute Instances (VMs) ---
    try:
        logging.info(f"Fetching Compute Instances in '{compartment.name}' ({region_name})")
        instances = oci.pagination.list_call_get_all_results(compute_client.list_instances,
                                                             compartment_id=compartment.id).data
        for instance in instances:
            tags = get_resource_tags(instance)
            details = {
                "Resource Type": "Compute Instance", "Name": instance.display_name, "Shape": instance.shape,
                "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
                "Compartment ID": compartment.id, "Region": region_name
            }

            # Get network information
            try:
                vnic_attachments = oci.pagination.list_call_get_all_results(compute_client.list_vnic_attachments,
                                                                            compartment_id=compartment.id,
                                                                            instance_id=instance.id).data
                if vnic_attachments:
                    vnic = network_client.get_vnic(vnic_attachments[0].vnic_id).data
                    details["Private IP"] = vnic.private_ip or "N/A"
                    details["Public IP"] = vnic.public_ip or "N/A"
                    subnet_info = subnet_map.get(vnic.subnet_id, ("N/A", "N/A"))
                    details["Subnet Name"] = subnet_info[0]
                    details["VCN Name"] = subnet_info[1]
            except oci.exceptions.ServiceError:
                details["Private IP"], details["Public IP"] = "Error", "Error"

            # Get backup status for boot volume
            backup_status, backup_policy, backup_retention = get_boot_volume_backup_status(
                compute_client, blockstorage_client, instance.id, compartment.id
            )
            details["Backup Status"] = backup_status
            details["Backup Policy"] = backup_policy
            details["Backup Retention"] = backup_retention

            all_resources.append(details)
    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not fetch Instances in '{compartment.name}' ({region_name}): {e}")

    # --- 3. OKE Clusters and Node Pools ---
    try:
        logging.info(f"Fetching OKE Clusters in '{compartment.name}' ({region_name})")
        clusters = oci.pagination.list_call_get_all_results(oke_client.list_clusters,
                                                            compartment_id=compartment.id).data
        for cluster in clusters:
            tags = get_resource_tags(cluster)
            all_resources.append({
                "Resource Type": "OKE Cluster", "Name": cluster.name, "VCN Name": vcn_map.get(cluster.vcn_id, "N/A"),
                "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
                "Compartment ID": compartment.id, "Region": region_name
            })
            node_pools = oci.pagination.list_call_get_all_results(oke_client.list_node_pools,
                                                                  compartment_id=compartment.id,
                                                                  cluster_id=cluster.id).data
            for pool in node_pools:
                pool_tags = get_resource_tags(pool)
                all_resources.append({
                    "Resource Type": "OKE Node Pool", "Name": pool.name, "Shape": pool.node_shape,
                    "Node Count": pool.node_config_details.size, "Parent Cluster": cluster.name,
                    "Environment": pool_tags.get(ENVIRONMENT_TAG_KEY, "N/A"),
                    "Purpose": pool_tags.get(PURPOSE_TAG_KEY, "N/A"),
                    "Compartment ID": compartment.id, "Region": region_name
                })
    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not fetch OKE data in '{compartment.name}' ({region_name}): {e}")

    # --- 4. Load Balancers ---
    try:
        logging.info(f"Fetching Load Balancers in '{compartment.name}' ({region_name})")
        lbs = oci.pagination.list_call_get_all_results(load_balancer_client.list_load_balancers,
                                                       compartment_id=compartment.id).data
        for lb in lbs:
            tags = get_resource_tags(lb)
            public_ips = [ip.ip_address for ip in lb.ip_addresses if ip.is_public]
            all_resources.append({
                "Resource Type": "Load Balancer", "Name": lb.display_name, "Shape": lb.shape_name,
                "Public IP": ', '.join(public_ips) if public_ips else "N/A",
                "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
                "Compartment ID": compartment.id, "Region": region_name
            })
    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not fetch Load Balancers in '{compartment.name}' ({region_name}): {e}")

    # --- 5. MySQL Database Systems ---
    try:
        logging.info(f"Fetching MySQL Databases in '{compartment.name}' ({region_name})")
        # The list call only returns a summary, so we need to get full details for each DB
        db_summaries = oci.pagination.list_call_get_all_results(mysql_client.list_db_systems,
                                                                compartment_id=compartment.id).data
        for db_summary in db_summaries:
            try:
                # FIX: Make a specific call to get full details, which includes the subnet_id
                db = mysql_client.get_db_system(db_system_id=db_summary.id).data

                tags = get_resource_tags(db)
                db_type = "MySQL HeatWave DB" if db.is_heat_wave_cluster_attached else "MySQL DB System"
                subnet_info = subnet_map.get(db.subnet_id, ("N/A", "N/A"))

                # Get backup status
                backup_status, backup_policy, backup_retention = get_mysql_backup_status(db)

                all_resources.append({
                    "Resource Type": db_type, "Name": db.display_name, "Shape": db.shape_name,
                    "VCN Name": subnet_info[1],
                    "Subnet Name": subnet_info[0],
                    "Environment": tags.get(ENVIRONMENT_TAG_KEY, "N/A"), "Purpose": tags.get(PURPOSE_TAG_KEY, "N/A"),
                    "Backup Status": backup_status, "Backup Policy": backup_policy, "Backup Retention": backup_retention,
                    "Compartment ID": compartment.id, "Region": region_name
                })
            except oci.exceptions.ServiceError as detail_e:
                logging.error(f"Could not get full details for DB '{db_summary.display_name}': {detail_e}")

    except oci.exceptions.ServiceError as e:
        logging.warning(f"Could not fetch MySQL DBs in '{compartment.name}' ({region_name}): {e}")

    return all_resources


def main():
    """Main function to orchestrate the fetching and exporting process."""
    logging.info("--- Starting OCI Inventory Export ---")
    try:
        config = from_file(profile_name=CONFIG_PROFILE)
        tenancy_id = config["tenancy"]
        identity_client = oci.identity.IdentityClient(config)
    except Exception as e:
        logging.error(f"Failed to load OCI config. Error: {e}")
        return

    all_compartments = get_all_compartments(identity_client, tenancy_id)
    subscribed_regions = get_subscribed_regions(identity_client, tenancy_id)

    if not all_compartments: return
    if not subscribed_regions: subscribed_regions = [config['region']]

    root_compartment = identity_client.get_compartment(tenancy_id).data
    all_compartments.append(root_compartment)
    compartment_map = {c.id: c.name for c in all_compartments}
    compartment_map[tenancy_id] = f"{root_compartment.name} (Tenancy/Root)"

    full_inventory = []
    for compartment in all_compartments:
        for region in subscribed_regions:
            full_inventory.extend(process_compartment_resources(config, compartment, region))

    if not full_inventory:
        logging.warning("No resources found. The output file will be empty.")
        return

    df = pd.DataFrame(full_inventory)
    df['Compartment Name'] = df['Compartment ID'].map(compartment_map)

    # --- Split data into two dataframes for two sheets ---
    main_resources_df = df[~df['Resource Type'].isin(['VCN', 'Subnet'])].copy()
    network_df = df[df['Resource Type'].isin(['VCN', 'Subnet'])].copy()

    # --- Define final columns for each sheet ---
    main_cols = [
        "Compartment Name", "Region", "Environment", "Resource Type", "Name",
        "Purpose", "Shape", "Public IP", "Private IP", "Node Count",
        "Parent Cluster", "VCN Name", "Subnet Name", "Backup Status", "Backup Policy", "Backup Retention"
    ]
    network_cols = [
        "Compartment Name", "Region", "Environment", "Resource Type", "Name", "Purpose", "CIDR Block", "VCN Name"
    ]

    # Filter to only existing columns to prevent errors
    main_resources_df = main_resources_df[[col for col in main_cols if col in main_resources_df.columns]]
    network_df = network_df[[col for col in network_cols if col in network_df.columns]]

    # Sort the data
    main_resources_df.sort_values(by=["Compartment Name", "Environment", "Resource Type", "Name"], inplace=True)
    network_df.sort_values(by=["Compartment Name", "Region", "VCN Name", "Name"], inplace=True)

    logging.info(f"Exporting inventory to {OUTPUT_FILE_NAME}...")
    try:
        with pd.ExcelWriter(OUTPUT_FILE_NAME, engine='openpyxl') as writer:
            main_resources_df.to_excel(writer, sheet_name='Master_Inventory', index=False)
            network_df.to_excel(writer, sheet_name='Network_Layout', index=False)
        logging.info(f"--- Successfully created inventory file: {OUTPUT_FILE_NAME} ---")
    except Exception as e:
        logging.error(f"Failed to write to Excel file: {e}")


if __name__ == "__main__":
    main()
