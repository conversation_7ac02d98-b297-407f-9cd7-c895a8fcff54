import React from 'react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement } from 'chart.js';
import { <PERSON>hn<PERSON>, Bar } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement);

const Dashboard = ({ stats, resources, onRefresh }) => {
  if (!stats) {
    return (
      <div className="text-center py-8">
        <div className="loading-spinner h-8 w-8 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading dashboard...</p>
      </div>
    );
  }

  // Prepare chart data
  const resourceTypeData = {
    labels: Object.keys(stats.resources_by_type || {}),
    datasets: [
      {
        data: Object.values(stats.resources_by_type || {}),
        backgroundColor: [
          '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4',
          '#84CC16', '#F97316', '#EC4899', '#6366F1'
        ],
        borderWidth: 2,
        borderColor: '#ffffff'
      }
    ]
  };

  const environmentData = {
    labels: Object.keys(stats.resources_by_environment || {}),
    datasets: [
      {
        label: 'Resources',
        data: Object.values(stats.resources_by_environment || {}),
        backgroundColor: '#3B82F6',
        borderColor: '#1D4ED8',
        borderWidth: 1
      }
    ]
  };

  const regionData = {
    labels: Object.keys(stats.resources_by_region || {}),
    datasets: [
      {
        label: 'Resources',
        data: Object.values(stats.resources_by_region || {}),
        backgroundColor: '#10B981',
        borderColor: '#047857',
        borderWidth: 1
      }
    ]
  };

  const compartmentData = {
    labels: Object.keys(stats.resources_by_compartment || {}),
    datasets: [
      {
        label: 'Resources',
        data: Object.values(stats.resources_by_compartment || {}),
        backgroundColor: '#8B5CF6',
        borderColor: '#7C3AED',
        borderWidth: 1
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom'
      }
    }
  };

  const barChartOptions = {
    ...chartOptions,
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  const formatLastScan = (lastScan) => {
    if (!lastScan) return 'Never';
    const date = new Date(lastScan);
    return date.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="stat-card">
          <div className="stat-card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="stat-icon bg-blue-500">
                  <span>R</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Resources
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.total_resources?.toLocaleString() || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="stat-icon bg-green-500">
                  <span>C</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Compartments
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.total_compartments || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="stat-icon bg-purple-500">
                  <span>R</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Regions
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.total_regions || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="stat-icon bg-yellow-500">
                  <span>S</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Last Scan
                  </dt>
                  <dd className="text-sm font-medium text-gray-900">
                    {formatLastScan(stats.last_scan)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="chart-container">
          <h3 className="chart-title">Resources by Type</h3>
          <div style={{ height: '300px' }}>
            {Object.keys(stats.resources_by_type || {}).length > 0 ? (
              <Doughnut data={resourceTypeData} options={chartOptions} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data available
              </div>
            )}
          </div>
        </div>

        <div className="chart-container">
          <h3 className="chart-title">Resources by Environment</h3>
          <div style={{ height: '300px' }}>
            {Object.keys(stats.resources_by_environment || {}).length > 0 ? (
              <Bar data={environmentData} options={barChartOptions} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Additional Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="chart-container">
          <h3 className="chart-title">Resources by Region</h3>
          <div style={{ height: '300px' }}>
            {Object.keys(stats.resources_by_region || {}).length > 0 ? (
              <Bar data={regionData} options={barChartOptions} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data available
              </div>
            )}
          </div>
        </div>

        <div className="chart-container">
          <h3 className="chart-title">Resources by Compartment</h3>
          <div style={{ height: '300px' }}>
            {Object.keys(stats.resources_by_compartment || {}).length > 0 ? (
              <Bar data={compartmentData} options={barChartOptions} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                No data available
              </div>
            )}
          </div>
        </div>

        <div className="chart-container">
          <h3 className="chart-title">Quick Actions</h3>
          <div className="space-y-4">
            <button
              onClick={onRefresh}
              className="w-full btn-primary"
            >
              🔄 Refresh Dashboard
            </button>
            <button
              onClick={() => window.open('/api/v1/docs', '_blank')}
              className="w-full btn-secondary"
            >
              📚 API Documentation
            </button>
            <button
              onClick={() => {
                const params = new URLSearchParams();
                window.open(`/api/v1/export/excel?${params.toString()}`, '_blank');
              }}
              className="w-full btn-warning"
            >
              📊 Export All Data
            </button>
          </div>
        </div>
      </div>

      {/* Recent Resources Summary */}
      <div className="chart-container">
        <h3 className="chart-title">Resource Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {Object.values(stats.resources_by_type || {}).reduce((a, b) => a + b, 0)}
            </div>
            <div className="text-sm text-blue-800">Total Resources</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {Object.keys(stats.resources_by_environment || {}).length}
            </div>
            <div className="text-sm text-green-800">Environments</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(stats.resources_by_type || {}).length}
            </div>
            <div className="text-sm text-purple-800">Resource Types</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
