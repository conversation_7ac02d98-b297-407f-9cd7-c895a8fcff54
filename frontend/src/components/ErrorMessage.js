import React from 'react';

const ErrorMessage = ({ 
  title = 'Something went wrong', 
  message, 
  onRetry, 
  retryText = 'Try Again' 
}) => {
  return (
    <div className="text-center py-8">
      <div className="text-red-500 text-6xl mb-4">⚠️</div>
      <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
      {message && (
        <p className="text-gray-600 mb-4 max-w-md mx-auto">{message}</p>
      )}
      {onRetry && (
        <button 
          onClick={onRetry}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          {retryText}
        </button>
      )}
    </div>
  );
};

export default ErrorMessage;
