import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ColumnConfiguration from './ColumnConfiguration';
import TablePresets from './TablePresets';

const API_BASE = process.env.REACT_APP_API_URL || '/api/v1';

// Define all available columns with their configuration
const AVAILABLE_COLUMNS = {
  name: {
    key: 'name',
    label: 'Name',
    sortable: true,
    defaultVisible: true,
    type: 'primary',
    width: 'w-48'
  },
  resource_type: {
    key: 'resource_type',
    label: 'Type',
    sortable: true,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-32'
  },
  environment: {
    key: 'environment',
    label: 'Environment',
    sortable: true,
    defaultVisible: true,
    type: 'badge',
    width: 'w-28'
  },
  region_name: {
    key: 'region_name',
    label: 'Region',
    sortable: true,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-32'
  },
  compartment_name: {
    key: 'compartment_name',
    label: 'Compartment',
    sortable: true,
    defaultVisible: false,
    type: 'secondary',
    width: 'w-40'
  },
  public_ip: {
    key: 'public_ip',
    label: 'Public IP',
    sortable: false,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-36'
  },
  private_ip: {
    key: 'private_ip',
    label: 'Private IP',
    sortable: false,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-36'
  },
  vcn_name: {
    key: 'vcn_name',
    label: 'VCN',
    sortable: true,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-32'
  },
  subnet_name: {
    key: 'subnet_name',
    label: 'Subnet',
    sortable: true,
    defaultVisible: false,
    type: 'secondary',
    width: 'w-32'
  },
  shape: {
    key: 'shape',
    label: 'Shape',
    sortable: true,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-32'
  },
  purpose: {
    key: 'purpose',
    label: 'Purpose',
    sortable: true,
    defaultVisible: true,
    type: 'secondary',
    width: 'w-32'
  },
  lifecycle_state: {
    key: 'lifecycle_state',
    label: 'State',
    sortable: true,
    defaultVisible: false,
    type: 'status',
    width: 'w-24'
  },
  time_created: {
    key: 'time_created',
    label: 'Created',
    sortable: true,
    defaultVisible: false,
    type: 'date',
    width: 'w-32'
  },
  node_count: {
    key: 'node_count',
    label: 'Nodes',
    sortable: true,
    defaultVisible: false,
    type: 'number',
    width: 'w-20'
  },
  cidr_block: {
    key: 'cidr_block',
    label: 'CIDR',
    sortable: false,
    defaultVisible: false,
    type: 'secondary',
    width: 'w-32'
  }
};

// Default column order
const DEFAULT_COLUMN_ORDER = [
  'name', 'resource_type', 'environment', 'region_name',
  'public_ip', 'private_ip', 'vcn_name', 'shape', 'purpose'
];

const ResourceTable = ({ resources: initialResources, onRefresh }) => {
  const [resources, setResources] = useState(initialResources || []);
  const [filteredResources, setFilteredResources] = useState(initialResources || []);
  const [filters, setFilters] = useState({
    environment: '',
    resourceType: '',
    region: '',
    compartment: '',
    search: ''
  });
  const [filterOptions, setFilterOptions] = useState({
    environments: [],
    resourceTypes: [],
    regions: [],
    compartments: []
  });
  const [loading, setLoading] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  // Column configuration state
  const [visibleColumns, setVisibleColumns] = useState(() => {
    const saved = localStorage.getItem('oci-table-columns');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to parse saved column configuration');
      }
    }
    // Default visible columns
    return Object.keys(AVAILABLE_COLUMNS).filter(key => AVAILABLE_COLUMNS[key].defaultVisible);
  });

  const [columnOrder, setColumnOrder] = useState(() => {
    const saved = localStorage.getItem('oci-table-column-order');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to parse saved column order');
      }
    }
    return DEFAULT_COLUMN_ORDER;
  });

  const [showColumnConfig, setShowColumnConfig] = useState(false);
  const [showPresets, setShowPresets] = useState(false);

  useEffect(() => {
    setResources(initialResources || []);
    setFilteredResources(initialResources || []);
    loadFilterOptions();
  }, [initialResources]);

  useEffect(() => {
    applyFilters();
  }, [filters, sortConfig]);

  const loadFilterOptions = async () => {
    try {
      const [envResponse, typeResponse, regionResponse, compartmentResponse] = await Promise.all([
        axios.get(`${API_BASE}/filters/values?field=environment`),
        axios.get(`${API_BASE}/filters/values?field=resource_type`),
        axios.get(`${API_BASE}/filters/values?field=region_name`),
        axios.get(`${API_BASE}/filters/values?field=compartment_name`)
      ]);

      setFilterOptions({
        environments: envResponse.data.values || [],
        resourceTypes: typeResponse.data.values || [],
        regions: regionResponse.data.values || [],
        compartments: compartmentResponse.data.values || []
      });
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  const applyFilters = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams();
      if (filters.environment) params.append('environment', filters.environment);
      if (filters.resourceType) params.append('resource_type', filters.resourceType);
      if (filters.region) params.append('region_name', filters.region);
      if (filters.compartment) params.append('compartment_name', filters.compartment);
      if (filters.search) params.append('search', filters.search);

      const response = await axios.get(`${API_BASE}/resources?${params.toString()}`);
      let filtered = response.data;

      // Apply sorting
      if (sortConfig.key) {
        filtered.sort((a, b) => {
          const aValue = a[sortConfig.key] || '';
          const bValue = b[sortConfig.key] || '';

          if (aValue < bValue) {
            return sortConfig.direction === 'asc' ? -1 : 1;
          }
          if (aValue > bValue) {
            return sortConfig.direction === 'asc' ? 1 : -1;
          }
          return 0;
        });
      }

      setFilteredResources(filtered);
    } catch (error) {
      console.error('Error applying filters:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleFilterChange = (filterKey, value) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      environment: '',
      resourceType: '',
      region: '',
      compartment: '',
      search: ''
    });
  };

  // Column configuration functions
  const toggleColumnVisibility = (columnKey) => {
    const newVisibleColumns = visibleColumns.includes(columnKey)
      ? visibleColumns.filter(key => key !== columnKey)
      : [...visibleColumns, columnKey];

    setVisibleColumns(newVisibleColumns);
    localStorage.setItem('oci-table-columns', JSON.stringify(newVisibleColumns));
  };

  const resetColumns = () => {
    const defaultColumns = Object.keys(AVAILABLE_COLUMNS).filter(key => AVAILABLE_COLUMNS[key].defaultVisible);
    setVisibleColumns(defaultColumns);
    setColumnOrder(DEFAULT_COLUMN_ORDER);
    localStorage.setItem('oci-table-columns', JSON.stringify(defaultColumns));
    localStorage.setItem('oci-table-column-order', JSON.stringify(DEFAULT_COLUMN_ORDER));
  };

  const getOrderedVisibleColumns = () => {
    return columnOrder.filter(key => visibleColumns.includes(key));
  };

  const moveColumn = (fromIndex, toIndex) => {
    const orderedColumns = getOrderedVisibleColumns();
    const newOrder = [...orderedColumns];
    const [movedColumn] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, movedColumn);

    // Update the full column order
    const newColumnOrder = [...columnOrder];
    const visibleInOrder = getOrderedVisibleColumns();

    // Remove visible columns from their current positions
    visibleInOrder.forEach(col => {
      const index = newColumnOrder.indexOf(col);
      if (index > -1) newColumnOrder.splice(index, 1);
    });

    // Insert reordered visible columns at the beginning
    newOrder.forEach((col, index) => {
      newColumnOrder.splice(index, 0, col);
    });

    setColumnOrder(newColumnOrder);
    localStorage.setItem('oci-table-column-order', JSON.stringify(newColumnOrder));
  };

  const applyPreset = (presetColumns) => {
    setVisibleColumns(presetColumns);
    localStorage.setItem('oci-table-columns', JSON.stringify(presetColumns));
    setShowPresets(false);
  };

  const exportData = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.environment) params.append('environment', filters.environment);
      if (filters.resourceType) params.append('resource_type', filters.resourceType);
      if (filters.region) params.append('region_name', filters.region);
      if (filters.compartment) params.append('compartment_name', filters.compartment);

      const response = await axios.get(`${API_BASE}/export/excel?${params.toString()}`);
      alert('Export completed! File saved to: ' + response.data.file_path);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Error exporting data. Please try again.');
    }
  };

  const getEnvironmentBadge = (environment) => {
    if (!environment || environment === 'N/A') return null;
    
    const envClass = {
      'prd': 'env-prd',
      'prod': 'env-prd',
      'production': 'env-prd',
      'stg': 'env-stg',
      'staging': 'env-stg',
      'test': 'env-test',
      'tst': 'env-test',
      'dev': 'env-dev',
      'development': 'env-dev'
    };

    const className = envClass[environment.toLowerCase()] || 'bg-gray-100 text-gray-800';
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${className}`}>
        {environment}
      </span>
    );
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return <span className="text-gray-400">↕️</span>;
    }
    return sortConfig.direction === 'asc' ? <span>↑</span> : <span>↓</span>;
  };

  // Helper function to render cell content based on column type
  const renderCellContent = (resource, column) => {
    const value = resource[column.key];

    switch (column.type) {
      case 'badge':
        if (column.key === 'environment') {
          return getEnvironmentBadge(value);
        }
        return value || 'N/A';

      case 'status':
        if (!value || value === 'N/A') return 'N/A';
        const statusClass = value.toLowerCase() === 'running' || value.toLowerCase() === 'active'
          ? 'status-active'
          : value.toLowerCase() === 'stopped' || value.toLowerCase() === 'inactive'
          ? 'status-inactive'
          : 'status-pending';
        return <span className={statusClass}>{value}</span>;

      case 'date':
        if (!value) return 'N/A';
        return new Date(value).toLocaleDateString();

      case 'number':
        return value || 0;

      case 'primary':
        return value || 'N/A';

      case 'secondary':
      default:
        return value || 'N/A';
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="filter-container">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Filters</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Environment</label>
            <select
              value={filters.environment}
              onChange={(e) => handleFilterChange('environment', e.target.value)}
              className="form-select"
            >
              <option value="">All Environments</option>
              {filterOptions.environments.map(env => (
                <option key={env} value={env}>{env}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Compartment</label>
            <select
              value={filters.compartment}
              onChange={(e) => handleFilterChange('compartment', e.target.value)}
              className="form-select"
            >
              <option value="">All Compartments</option>
              {filterOptions.compartments.map(comp => (
                <option key={comp} value={comp}>{comp}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Resource Type</label>
            <select
              value={filters.resourceType}
              onChange={(e) => handleFilterChange('resourceType', e.target.value)}
              className="form-select"
            >
              <option value="">All Types</option>
              {filterOptions.resourceTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Region</label>
            <select
              value={filters.region}
              onChange={(e) => handleFilterChange('region', e.target.value)}
              className="form-select"
            >
              <option value="">All Regions</option>
              {filterOptions.regions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search resources..."
              className="form-input"
            />
          </div>
        </div>
        
        <div className="mt-4 flex flex-wrap gap-2">
          <button onClick={clearFilters} className="btn-secondary">
            Clear Filters
          </button>
          <button onClick={exportData} className="btn-warning">
            Export to Excel
          </button>
          <button
            onClick={() => setShowPresets(!showPresets)}
            className="btn-secondary"
          >
            📋 Presets
          </button>
          <button
            onClick={() => setShowColumnConfig(!showColumnConfig)}
            className="btn-secondary"
          >
            ⚙️ Customize Columns
          </button>
          <button onClick={onRefresh} className="btn-primary">
            Refresh Data
          </button>
        </div>
      </div>

      {/* Table Presets Panel */}
      {showPresets && (
        <TablePresets
          onApplyPreset={applyPreset}
          currentColumns={visibleColumns}
          onClose={() => setShowPresets(false)}
        />
      )}

      {/* Column Configuration Panel */}
      {showColumnConfig && (
        <ColumnConfiguration
          availableColumns={AVAILABLE_COLUMNS}
          visibleColumns={visibleColumns}
          onToggleColumn={toggleColumnVisibility}
          onResetColumns={resetColumns}
          onClose={() => setShowColumnConfig(false)}
        />
      )}

      {/* Results Summary */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600">
            Showing {filteredResources.length} of {resources.length} resources
          </p>
          <p className="text-sm text-gray-500">
            {getOrderedVisibleColumns().length} columns visible
          </p>
        </div>
      </div>

      {/* Resources Table */}
      <div className="table-container">
        <div className="table-header">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Resources</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Comprehensive list of all OCI resources
          </p>
        </div>
        
        <div className="table-responsive">
          <table className="table-main">
            <thead className="table-head">
              <tr>
                {getOrderedVisibleColumns().map((columnKey) => {
                  const column = AVAILABLE_COLUMNS[columnKey];
                  return (
                    <th
                      key={columnKey}
                      className={`table-head-cell ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}`}
                      onClick={column.sortable ? () => handleSort(columnKey) : undefined}
                    >
                      {column.label} {column.sortable && getSortIcon(columnKey)}
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredResources.map((resource, index) => (
                <tr key={resource.id || index} className="hover:bg-gray-50">
                  {getOrderedVisibleColumns().map((columnKey) => {
                    const column = AVAILABLE_COLUMNS[columnKey];
                    const cellClass = column.type === 'primary'
                      ? 'table-cell table-cell-primary'
                      : 'table-cell table-cell-secondary';

                    return (
                      <td key={columnKey} className={cellClass}>
                        {renderCellContent(resource, column)}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredResources.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No resources found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResourceTable;
