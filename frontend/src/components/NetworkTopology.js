import React, { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE = process.env.REACT_APP_API_URL || '/api/v1';

const NetworkTopology = ({ onRefresh }) => {
  const [networkData, setNetworkData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [groupedData, setGroupedData] = useState({});

  useEffect(() => {
    loadNetworkTopology();
  }, []);

  const loadNetworkTopology = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE}/network-topology`);
      const data = response.data;
      setNetworkData(data);
      
      // Group data by compartment and region
      const grouped = groupNetworkData(data);
      setGroupedData(grouped);
      
    } catch (err) {
      console.error('Error loading network topology:', err);
      setError('Failed to load network topology data');
    } finally {
      setLoading(false);
    }
  };

  const groupNetworkData = (data) => {
    const grouped = {};

    data.forEach(item => {
      const compartmentKey = item.compartment_name || item.compartment_id; // Use name, fallback to ID
      const regionKey = item.region_name;

      if (!grouped[compartmentKey]) {
        grouped[compartmentKey] = {};
      }
      if (!grouped[compartmentKey][regionKey]) {
        grouped[compartmentKey][regionKey] = {
          vcns: [],
          subnets: []
        };
      }

      if (item.resource_type === 'VCN') {
        grouped[compartmentKey][regionKey].vcns.push(item);
      } else if (item.resource_type === 'Subnet') {
        grouped[compartmentKey][regionKey].subnets.push(item);
      }
    });

    return grouped;
  };

  const getEnvironmentBadge = (environment) => {
    if (!environment || environment === 'N/A') return null;
    
    const envClass = {
      'prd': 'bg-red-100 text-red-800',
      'prod': 'bg-red-100 text-red-800',
      'production': 'bg-red-100 text-red-800',
      'stg': 'bg-yellow-100 text-yellow-800',
      'staging': 'bg-yellow-100 text-yellow-800',
      'test': 'bg-blue-100 text-blue-800',
      'tst': 'bg-blue-100 text-blue-800',
      'dev': 'bg-green-100 text-green-800',
      'development': 'bg-green-100 text-green-800'
    };

    const className = envClass[environment.toLowerCase()] || 'bg-gray-100 text-gray-800';
    
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${className}`}>
        {environment}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading network topology...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-500 text-4xl mb-4">⚠️</div>
        <p className="text-red-600 mb-4">{error}</p>
        <button 
          onClick={loadNetworkTopology}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Network Topology</h2>
            <p className="text-gray-600">VCNs and Subnets organized by compartment and region</p>
          </div>
          <button
            onClick={() => {
              loadNetworkTopology();
              onRefresh();
            }}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Network Topology Cards */}
      {Object.keys(groupedData).length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No network topology data available.</p>
          <p className="text-sm text-gray-400 mt-2">
            Run an inventory scan to populate network data.
          </p>
        </div>
      ) : (
        Object.entries(groupedData).map(([compartmentKey, regions]) => (
          <div key={compartmentKey} className="bg-white rounded-lg shadow overflow-hidden">
            <div className="bg-gray-50 px-6 py-4 border-b">
              <h3 className="text-lg font-medium text-gray-900">
                Compartment: {compartmentKey}
              </h3>
            </div>
            
            <div className="p-6">
              {Object.entries(regions).map(([regionName, data]) => (
                <div key={regionName} className="mb-8 last:mb-0">
                  <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm mr-2">
                      {regionName}
                    </span>
                    Region
                  </h4>
                  
                  {/* VCNs */}
                  {data.vcns.length > 0 && (
                    <div className="mb-6">
                      <h5 className="text-sm font-medium text-gray-700 mb-3">
                        Virtual Cloud Networks ({data.vcns.length})
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {data.vcns.map((vcn, index) => (
                          <div key={index} className="border rounded-lg p-4 bg-blue-50">
                            <div className="flex justify-between items-start mb-2">
                              <h6 className="font-medium text-gray-900">{vcn.name}</h6>
                              {getEnvironmentBadge(vcn.environment)}
                            </div>
                            <p className="text-sm text-gray-600 mb-1">
                              <span className="font-medium">CIDR:</span> {vcn.cidr_block}
                            </p>
                            {vcn.purpose && vcn.purpose !== 'N/A' && (
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Purpose:</span> {vcn.purpose}
                              </p>
                            )}
                            
                            {/* Subnets for this VCN */}
                            {data.subnets.filter(subnet => subnet.vcn_name === vcn.name).length > 0 && (
                              <div className="mt-3 pt-3 border-t border-blue-200">
                                <p className="text-xs font-medium text-gray-700 mb-2">
                                  Subnets ({data.subnets.filter(subnet => subnet.vcn_name === vcn.name).length})
                                </p>
                                <div className="space-y-2">
                                  {data.subnets
                                    .filter(subnet => subnet.vcn_name === vcn.name)
                                    .map((subnet, subnetIndex) => (
                                      <div key={subnetIndex} className="bg-white rounded p-2 border">
                                        <div className="flex justify-between items-center">
                                          <span className="text-xs font-medium text-gray-800">
                                            {subnet.name}
                                          </span>
                                          {getEnvironmentBadge(subnet.environment)}
                                        </div>
                                        <p className="text-xs text-gray-600 mt-1">
                                          {subnet.cidr_block}
                                        </p>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Orphaned Subnets (subnets without matching VCN) */}
                  {data.subnets.filter(subnet => 
                    !data.vcns.some(vcn => vcn.name === subnet.vcn_name)
                  ).length > 0 && (
                    <div className="mb-6">
                      <h5 className="text-sm font-medium text-gray-700 mb-3">
                        Other Subnets ({data.subnets.filter(subnet => 
                          !data.vcns.some(vcn => vcn.name === subnet.vcn_name)
                        ).length})
                      </h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {data.subnets
                          .filter(subnet => !data.vcns.some(vcn => vcn.name === subnet.vcn_name))
                          .map((subnet, index) => (
                            <div key={index} className="border rounded-lg p-4 bg-gray-50">
                              <div className="flex justify-between items-start mb-2">
                                <h6 className="font-medium text-gray-900">{subnet.name}</h6>
                                {getEnvironmentBadge(subnet.environment)}
                              </div>
                              <p className="text-sm text-gray-600 mb-1">
                                <span className="font-medium">CIDR:</span> {subnet.cidr_block}
                              </p>
                              <p className="text-sm text-gray-600 mb-1">
                                <span className="font-medium">VCN:</span> {subnet.vcn_name}
                              </p>
                              {subnet.purpose && subnet.purpose !== 'N/A' && (
                                <p className="text-sm text-gray-600">
                                  <span className="font-medium">Purpose:</span> {subnet.purpose}
                                </p>
                              )}
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                  
                  {data.vcns.length === 0 && data.subnets.length === 0 && (
                    <p className="text-gray-500 text-sm">No network resources in this region.</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))
      )}

      {/* Summary Stats */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Network Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {networkData.filter(item => item.resource_type === 'VCN').length}
            </div>
            <div className="text-sm text-blue-800">Total VCNs</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {networkData.filter(item => item.resource_type === 'Subnet').length}
            </div>
            <div className="text-sm text-green-800">Total Subnets</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(groupedData).length}
            </div>
            <div className="text-sm text-purple-800">Compartments</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NetworkTopology;
