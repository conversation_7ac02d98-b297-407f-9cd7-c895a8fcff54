import React, { useState } from 'react';

const ColumnConfiguration = ({ 
  availableColumns, 
  visibleColumns, 
  onToggleColumn, 
  onResetColumns, 
  onClose 
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredColumns = Object.entries(availableColumns).filter(([key, column]) =>
    column.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getColumnStats = () => {
    const total = Object.keys(availableColumns).length;
    const visible = visibleColumns.length;
    return { total, visible };
  };

  const { total, visible } = getColumnStats();

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Customize Table Columns</h3>
          <p className="text-sm text-gray-500 mt-1">
            {visible} of {total} columns visible
          </p>
        </div>
        <div className="flex space-x-2">
          <button 
            onClick={onResetColumns} 
            className="btn-secondary text-sm"
            title="Reset to default columns"
          >
            Reset to Default
          </button>
          <button 
            onClick={onClose} 
            className="text-gray-400 hover:text-gray-600 text-xl leading-none"
            title="Close column configuration"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="Search columns..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="form-input w-full"
        />
      </div>

      {/* Quick Actions */}
      <div className="mb-4 flex space-x-2">
        <button
          onClick={() => {
            Object.keys(availableColumns).forEach(key => {
              if (!visibleColumns.includes(key)) {
                onToggleColumn(key);
              }
            });
          }}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Show All
        </button>
        <span className="text-gray-300">|</span>
        <button
          onClick={() => {
            visibleColumns.forEach(key => onToggleColumn(key));
          }}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Hide All
        </button>
        <span className="text-gray-300">|</span>
        <button
          onClick={() => {
            Object.keys(availableColumns).forEach(key => {
              const isVisible = visibleColumns.includes(key);
              if (isVisible !== availableColumns[key].defaultVisible) {
                onToggleColumn(key);
              }
            });
          }}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Toggle Selection
        </button>
      </div>

      {/* Column List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
        {filteredColumns.map(([key, column]) => (
          <label 
            key={key} 
            className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50 cursor-pointer"
          >
            <input
              type="checkbox"
              checked={visibleColumns.includes(key)}
              onChange={() => onToggleColumn(key)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <div className="flex-1 min-w-0">
              <span className="text-sm font-medium text-gray-700 block truncate">
                {column.label}
              </span>
              <span className="text-xs text-gray-500 capitalize">
                {column.type} • {column.sortable ? 'Sortable' : 'Non-sortable'}
              </span>
            </div>
          </label>
        ))}
      </div>

      {filteredColumns.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No columns found matching "{searchTerm}"</p>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Tips:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Column preferences are automatically saved</li>
          <li>• Use search to quickly find specific columns</li>
          <li>• Primary columns (like Name) are recommended to keep visible</li>
          <li>• Column reordering via drag & drop coming soon</li>
        </ul>
      </div>
    </div>
  );
};

export default ColumnConfiguration;
