// Table preset configurations for different use cases
export const TABLE_PRESETS = {
  default: {
    name: 'Default View',
    description: 'Standard resource overview',
    icon: '📋',
    columns: ['name', 'resource_type', 'environment', 'region_name', 'public_ip', 'private_ip', 'vcn_name', 'shape', 'purpose']
  },
  network: {
    name: 'Network View',
    description: 'Focus on network configuration',
    icon: '🌐',
    columns: ['name', 'resource_type', 'environment', 'region_name', 'compartment_name', 'vcn_name', 'subnet_name', 'public_ip', 'private_ip', 'cidr_block']
  },
  management: {
    name: 'Management View',
    description: 'Administrative and lifecycle info',
    icon: '⚙️',
    columns: ['name', 'resource_type', 'environment', 'compartment_name', 'region_name', 'lifecycle_state', 'time_created', 'purpose']
  },
  compute: {
    name: 'Compute View',
    description: 'Compute-specific details',
    icon: '💻',
    columns: ['name', 'resource_type', 'environment', 'region_name', 'shape', 'node_count', 'lifecycle_state', 'public_ip', 'private_ip']
  },
  minimal: {
    name: 'Minimal View',
    description: 'Essential information only',
    icon: '📝',
    columns: ['name', 'resource_type', 'environment', 'region_name']
  },
  detailed: {
    name: 'Detailed View',
    description: 'All available information',
    icon: '🔍',
    columns: ['name', 'resource_type', 'environment', 'compartment_name', 'region_name', 'vcn_name', 'subnet_name', 'public_ip', 'private_ip', 'shape', 'node_count', 'purpose', 'lifecycle_state', 'time_created', 'cidr_block']
  }
};

const TablePresets = ({ onApplyPreset, currentColumns, onClose }) => {
  const isCurrentPreset = (presetColumns) => {
    if (currentColumns.length !== presetColumns.length) return false;
    return presetColumns.every(col => currentColumns.includes(col));
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow border">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Table Presets</h3>
          <p className="text-sm text-gray-500 mt-1">
            Quick column configurations for different use cases
          </p>
        </div>
        <button 
          onClick={onClose} 
          className="text-gray-400 hover:text-gray-600 text-xl leading-none"
          title="Close presets"
        >
          ✕
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(TABLE_PRESETS).map(([key, preset]) => {
          const isCurrent = isCurrentPreset(preset.columns);
          
          return (
            <div
              key={key}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                isCurrent 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => onApplyPreset(preset.columns)}
            >
              <div className="flex items-start space-x-3">
                <span className="text-2xl">{preset.icon}</span>
                <div className="flex-1 min-w-0">
                  <h4 className={`font-medium ${isCurrent ? 'text-blue-900' : 'text-gray-900'}`}>
                    {preset.name}
                    {isCurrent && <span className="ml-2 text-xs text-blue-600">• Current</span>}
                  </h4>
                  <p className={`text-sm mt-1 ${isCurrent ? 'text-blue-700' : 'text-gray-600'}`}>
                    {preset.description}
                  </p>
                  <p className={`text-xs mt-2 ${isCurrent ? 'text-blue-600' : 'text-gray-500'}`}>
                    {preset.columns.length} columns
                  </p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">💡 About Presets:</h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• Click any preset to instantly apply its column configuration</li>
          <li>• Presets are designed for specific workflows and use cases</li>
          <li>• You can still customize columns after applying a preset</li>
          <li>• Your custom configurations are automatically saved</li>
        </ul>
      </div>
    </div>
  );
};

export default TablePresets;
