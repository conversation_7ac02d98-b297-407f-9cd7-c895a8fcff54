# OCI Inventory Dashboard - Frontend

A modern React-based frontend for the OCI Inventory Dashboard, providing an intuitive interface for managing and visualizing Oracle Cloud Infrastructure resources.

## 🎯 Features

### Dashboard View
- **Real-time Statistics**: Resource counts, compartments, regions
- **Interactive Charts**: Resource distribution by type, environment, and region
- **Quick Actions**: Refresh data, export reports, access API docs

### Resource Management
- **Advanced Filtering**: Filter by environment, resource type, region, and search
- **Sortable Tables**: Click column headers to sort data
- **Environment Badges**: Color-coded environment indicators (prod, staging, test, dev)
- **Export Functionality**: Export filtered data to Excel

### Network Topology
- **Visual Network Layout**: VCNs and subnets organized by compartment and region
- **Hierarchical View**: Subnets grouped under their parent VCNs
- **Environment Tagging**: Clear environment identification for network resources

## 🛠️ Technology Stack

- **React 18**: Modern React with hooks and functional components
- **Tailwind CSS**: Utility-first CSS framework for responsive design
- **Chart.js**: Beautiful, responsive charts and visualizations
- **Axios**: HTTP client for API communication
- **React Router**: Client-side routing (ready for expansion)

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- Backend API running on http://localhost:8000

### Installation

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Start development server**:
   ```bash
   npm start
   ```

3. **Or use the Python helper script**:
   ```bash
   python start_frontend.py
   ```

The application will open at http://localhost:3000 and automatically proxy API calls to the backend.

## 📁 Project Structure

```
frontend/
├── public/
│   ├── index.html          # Main HTML template
│   └── manifest.json       # PWA manifest
├── src/
│   ├── components/         # React components
│   │   ├── Dashboard.js    # Main dashboard with charts
│   │   ├── ResourceTable.js # Resource listing and filtering
│   │   └── NetworkTopology.js # Network visualization
│   ├── App.js             # Main application component
│   ├── App.css            # Application styles
│   ├── index.js           # React entry point
│   └── index.css          # Global styles
├── package.json           # Dependencies and scripts
├── tailwind.config.js     # Tailwind CSS configuration
└── postcss.config.js      # PostCSS configuration
```

## 🎨 Component Overview

### Dashboard Component
- Displays key metrics and statistics
- Interactive charts using Chart.js
- Quick action buttons for common tasks
- Responsive grid layout

### ResourceTable Component
- Comprehensive resource listing
- Advanced filtering and search
- Sortable columns
- Environment badges
- Export functionality

### NetworkTopology Component
- Visual representation of network structure
- Hierarchical VCN/Subnet organization
- Compartment and region grouping
- Environment-aware styling

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```bash
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_ENVIRONMENT=development
```

### Tailwind CSS
The project uses Tailwind CSS with custom configuration:
- Custom OCI brand colors
- Form plugin for better form styling
- Responsive design utilities

### API Integration
The frontend communicates with the FastAPI backend through:
- RESTful API endpoints
- Automatic error handling
- Loading states and user feedback

## 📊 Features in Detail

### Filtering System
- **Environment Filter**: Filter by prod, staging, test, dev
- **Resource Type Filter**: Filter by compute, OKE, databases, etc.
- **Region Filter**: Filter by OCI regions
- **Search**: Full-text search across resource names and properties

### Data Visualization
- **Doughnut Charts**: Resource distribution by type
- **Bar Charts**: Resource counts by environment and region
- **Summary Cards**: Key metrics at a glance
- **Network Diagrams**: Visual network topology

### User Experience
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Loading States**: Clear feedback during data loading
- **Error Handling**: Graceful error messages and retry options
- **Accessibility**: Keyboard navigation and screen reader support

## 🚀 Development

### Available Scripts

- `npm start`: Start development server
- `npm build`: Build for production
- `npm test`: Run tests
- `npm run eject`: Eject from Create React App

### Adding New Features

1. **New Components**: Add to `src/components/`
2. **New Routes**: Update `App.js` with React Router
3. **New API Calls**: Add to component files using axios
4. **Styling**: Use Tailwind CSS classes

### Code Style
- Functional components with hooks
- Consistent naming conventions
- Proper error handling
- Responsive design patterns

## 🔄 API Integration

The frontend integrates with these backend endpoints:

- `GET /api/v1/dashboard/stats` - Dashboard statistics
- `GET /api/v1/resources` - Resource listing with filtering
- `GET /api/v1/network-topology` - Network topology data
- `POST /api/v1/inventory/scan` - Trigger inventory scan
- `GET /api/v1/export/excel` - Export data to Excel

## 🎯 Future Enhancements

### Planned Features
- **Real-time Updates**: WebSocket integration for live data
- **Advanced Charts**: More visualization types and drill-down
- **User Authentication**: Role-based access control
- **Customizable Dashboards**: User-configurable layouts
- **Mobile App**: React Native version

### Performance Optimizations
- **Code Splitting**: Lazy loading of components
- **Caching**: Smart data caching strategies
- **Virtualization**: Virtual scrolling for large datasets
- **PWA Features**: Offline support and caching

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Errors**:
   - Ensure backend is running on port 8000
   - Check CORS configuration
   - Verify API endpoints

2. **Build Errors**:
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify Tailwind CSS configuration

3. **Styling Issues**:
   - Ensure Tailwind CSS is properly configured
   - Check PostCSS configuration
   - Verify CSS imports

### Getting Help
- Check browser console for errors
- Review network tab for API calls
- Ensure all dependencies are installed
- Verify environment variables

## 📝 Contributing

1. Follow React best practices
2. Use Tailwind CSS for styling
3. Add proper error handling
4. Include loading states
5. Test on multiple screen sizes
6. Document new components

## 📄 License

This project is part of the OCI Inventory Dashboard and follows the same license terms.
