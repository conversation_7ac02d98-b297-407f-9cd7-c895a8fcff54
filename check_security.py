#!/usr/bin/env python3
"""
Security check script to identify potentially sensitive files
"""
import os
import glob
from pathlib import Path

def check_sensitive_files():
    """Check for potentially sensitive files in the project directory"""
    print("🔍 Checking for sensitive files...")
    
    # Define patterns for sensitive files
    sensitive_patterns = [
        # Environment files
        '.env',
        '.env.local',
        '.env.*.local',
        
        # OCI credentials
        '*.pem',
        '*.key',
        '.oci/**/*',
        'oci_config',
        'config',
        
        # Database files
        '*.db',
        '*.sqlite',
        '*.sqlite3',
        
        # Export files with potentially sensitive data
        'exports/*.xlsx',
        'exports/*.csv',
        
        # Log files
        '*.log',
        'logs/**/*',
        
        # Backup files
        '*.bak',
        '*.backup',
        
        # Temporary files
        '*.tmp',
        '*.temp',
    ]
    
    found_files = []
    
    for pattern in sensitive_patterns:
        matches = glob.glob(pattern, recursive=True)
        for match in matches:
            if os.path.isfile(match):
                found_files.append(match)
    
    return found_files

def check_gitignore_exists():
    """Check if .gitignore file exists"""
    gitignore_path = Path('.gitignore')
    return gitignore_path.exists()

def check_env_example():
    """Check if .env.example exists but .env doesn't"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    return {
        'example_exists': env_example.exists(),
        'env_exists': env_file.exists()
    }

def scan_for_hardcoded_secrets():
    """Scan Python files for potential hardcoded secrets"""
    print("🔍 Scanning for hardcoded secrets in Python files...")
    
    suspicious_patterns = [
        'ocid1.',  # OCI identifiers
        'BEGIN PRIVATE KEY',
        'BEGIN RSA PRIVATE KEY',
        'password=',
        'secret=',
        'token=',
        'api_key=',
    ]
    
    python_files = glob.glob('**/*.py', recursive=True)
    issues = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            for line_num, line in enumerate(content.split('\n'), 1):
                for pattern in suspicious_patterns:
                    if pattern in line and not line.strip().startswith('#'):
                        # Skip if it's clearly a comment or example
                        if 'example' in line.lower() or 'placeholder' in line.lower():
                            continue
                        if 'TODO' in line or 'FIXME' in line:
                            continue
                        
                        issues.append({
                            'file': file_path,
                            'line': line_num,
                            'pattern': pattern,
                            'content': line.strip()
                        })
        except Exception as e:
            print(f"⚠️ Could not scan {file_path}: {e}")
    
    return issues

def main():
    """Main security check function"""
    print("🔐 OCI Inventory Dashboard - Security Check")
    print("=" * 50)
    
    # Check for .gitignore
    if check_gitignore_exists():
        print("✅ .gitignore file exists")
    else:
        print("❌ .gitignore file missing - create one!")
        return
    
    # Check for sensitive files
    sensitive_files = check_sensitive_files()
    if sensitive_files:
        print(f"\n⚠️ Found {len(sensitive_files)} potentially sensitive files:")
        for file_path in sensitive_files:
            print(f"  - {file_path}")
        print("\n💡 These files should be in .gitignore or removed")
    else:
        print("✅ No sensitive files found in project directory")
    
    # Check environment file setup
    env_status = check_env_example()
    if env_status['example_exists'] and not env_status['env_exists']:
        print("✅ Good: .env.example exists but .env doesn't (create .env from example)")
    elif env_status['example_exists'] and env_status['env_exists']:
        print("⚠️ Both .env.example and .env exist (make sure .env is in .gitignore)")
    elif not env_status['example_exists']:
        print("❌ .env.example missing - create one as a template")
    
    # Scan for hardcoded secrets
    secret_issues = scan_for_hardcoded_secrets()
    if secret_issues:
        print(f"\n🚨 Found {len(secret_issues)} potential hardcoded secrets:")
        for issue in secret_issues:
            print(f"  - {issue['file']}:{issue['line']} - {issue['pattern']}")
            print(f"    {issue['content']}")
        print("\n💡 Move these to environment variables!")
    else:
        print("✅ No hardcoded secrets detected in Python files")
    
    # Git status check
    try:
        import subprocess
        result = subprocess.run(['git', 'status', '--porcelain'], 
                              capture_output=True, text=True, cwd='.')
        if result.returncode == 0:
            staged_files = [line[3:] for line in result.stdout.split('\n') 
                          if line.startswith('A ') or line.startswith('M ')]
            
            # Check if any staged files are sensitive
            sensitive_staged = []
            for file_path in staged_files:
                if any(pattern in file_path for pattern in ['.env', '.pem', '.key', '.db', '.sqlite']):
                    sensitive_staged.append(file_path)
            
            if sensitive_staged:
                print(f"\n🚨 DANGER: Sensitive files are staged for commit:")
                for file_path in sensitive_staged:
                    print(f"  - {file_path}")
                print("\n💡 Run: git reset HEAD <file> to unstage")
            else:
                print("✅ No sensitive files staged for commit")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Could not check git status (not a git repo or git not installed)")
    
    # Summary
    total_issues = len(sensitive_files) + len(secret_issues)
    if total_issues == 0:
        print(f"\n🎉 Security check passed! No issues found.")
        print("\n📋 Security checklist:")
        print("  ✅ .gitignore file exists")
        print("  ✅ No sensitive files in project")
        print("  ✅ No hardcoded secrets detected")
        print("  ✅ No sensitive files staged for commit")
    else:
        print(f"\n⚠️ Security check found {total_issues} issues to address.")
        print("\n📖 See SECURITY.md for detailed guidelines")

if __name__ == "__main__":
    main()
