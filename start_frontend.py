#!/usr/bin/env python3
"""
Frontend development server starter
"""
import subprocess
import sys
import os
import time
from pathlib import Path

def check_node():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js version: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def check_npm():
    """Check if npm is installed"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm version: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_dependencies():
    """Install npm dependencies"""
    print("📦 Installing npm dependencies...")
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    try:
        result = subprocess.run(
            ['npm', 'install'], 
            cwd=frontend_dir, 
            check=True,
            capture_output=True,
            text=True
        )
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print(f"Error output: {e.stderr}")
        return False

def start_react_dev_server():
    """Start the React development server"""
    print("🚀 Starting React development server...")
    frontend_dir = Path("frontend")
    
    try:
        # Set environment variable for API URL
        env = os.environ.copy()
        env['REACT_APP_API_URL'] = 'http://localhost:8000/api/v1'
        
        subprocess.run(
            ['npm', 'start'], 
            cwd=frontend_dir,
            env=env
        )
    except KeyboardInterrupt:
        print("\n🛑 React development server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start React server: {e}")

def main():
    """Main function"""
    print("🎨 Starting OCI Inventory Dashboard Frontend")
    print("=" * 50)
    
    # Check prerequisites
    if not check_node():
        print("❌ Node.js is not installed. Please install Node.js first:")
        print("   https://nodejs.org/")
        sys.exit(1)
    
    if not check_npm():
        print("❌ npm is not installed. Please install npm first.")
        sys.exit(1)
    
    # Check if backend is running
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is running")
        else:
            print("⚠️ Backend API might not be running properly")
    except:
        print("⚠️ Backend API is not running. Please start it first:")
        print("   python run_dev.py")
        print("\nContinuing anyway...")
    
    # Install dependencies if needed
    frontend_dir = Path("frontend")
    node_modules = frontend_dir / "node_modules"
    
    if not node_modules.exists():
        if not install_dependencies():
            sys.exit(1)
    else:
        print("✅ Dependencies already installed")
    
    print("\n🎉 Starting React development server...")
    print("📱 Frontend will be available at: http://localhost:3000")
    print("🔗 It will proxy API calls to: http://localhost:8000")
    print("\nPress Ctrl+C to stop the server")
    
    # Start the React development server
    start_react_dev_server()

if __name__ == "__main__":
    main()
