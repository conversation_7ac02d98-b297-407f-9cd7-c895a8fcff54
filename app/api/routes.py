"""
FastAPI routes for the OCI Inventory Dashboard
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from app import crud, schemas, models
from app.database import get_db
from app.services.inventory_service import InventoryService

router = APIRouter()


@router.get("/dashboard/stats", response_model=schemas.DashboardStats)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """Get dashboard statistics"""
    return crud.get_dashboard_stats(db)


@router.get("/resources")
async def get_resources(
    skip: int = 0,
    limit: int = 1000,
    compartment_id: Optional[str] = None,
    compartment_name: Optional[str] = None,
    region_name: Optional[str] = None,
    environment: Optional[str] = None,
    resource_type: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get resources with optional filtering"""
    filters = schemas.ResourceFilter(
        compartment_id=compartment_id,
        compartment_name=compartment_name,
        region_name=region_name,
        environment=environment,
        resource_type=resource_type,
        search=search
    )
    return crud.get_resources_with_compartment_names(db, skip=skip, limit=limit, filters=filters)


@router.get("/compartments", response_model=List[schemas.Compartment])
async def get_compartments(db: Session = Depends(get_db)):
    """Get all compartments"""
    return crud.get_compartments(db)


@router.get("/regions", response_model=List[schemas.Region])
async def get_regions(db: Session = Depends(get_db)):
    """Get all regions"""
    return crud.get_regions(db)


@router.get("/network-topology")
async def get_network_topology(db: Session = Depends(get_db)):
    """Get network topology data"""
    return crud.get_network_topology_with_compartment_names(db)


@router.post("/inventory/scan")
async def trigger_inventory_scan(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Trigger a new inventory scan"""
    inventory_service = InventoryService()
    scan = crud.create_inventory_scan(db, schemas.InventoryScanCreate())
    
    # Run scan in background
    background_tasks.add_task(inventory_service.run_full_scan, scan.id)
    
    return {"message": "Inventory scan started", "scan_id": scan.id}


@router.get("/inventory/scan/status")
async def get_scan_status(db: Session = Depends(get_db)):
    """Get the status of the latest inventory scan"""
    latest_scan = crud.get_latest_scan(db)
    if not latest_scan:
        return {"status": "no_scans", "message": "No scans have been run yet"}
    
    return {
        "scan_id": latest_scan.id,
        "status": latest_scan.status,
        "scan_started": latest_scan.scan_started,
        "scan_completed": latest_scan.scan_completed,
        "total_resources": latest_scan.total_resources,
        "total_compartments": latest_scan.total_compartments,
        "total_regions": latest_scan.total_regions,
        "error_message": latest_scan.error_message,
        "scan_duration_seconds": latest_scan.scan_duration_seconds
    }


@router.get("/filters/values")
async def get_filter_values(field: str, db: Session = Depends(get_db)):
    """Get unique values for filter dropdowns"""
    if field not in ["compartment_name", "region_name", "environment", "resource_type"]:
        raise HTTPException(status_code=400, detail="Invalid field")
    
    values = crud.get_unique_values(db, field)
    return {"field": field, "values": values}


@router.get("/export/excel")
async def export_to_excel(
    compartment_id: Optional[str] = None,
    compartment_name: Optional[str] = None,
    region_name: Optional[str] = None,
    environment: Optional[str] = None,
    resource_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Export filtered data to Excel"""
    from app.services.export_service import ExportService

    filters = schemas.ResourceFilter(
        compartment_id=compartment_id,
        compartment_name=compartment_name,
        region_name=region_name,
        environment=environment,
        resource_type=resource_type
    )

    export_service = ExportService()
    file_path = export_service.export_to_excel(db, filters)

    return {"message": "Export completed", "file_path": file_path}


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "OCI Inventory Dashboard"}


# Resource-specific endpoints for detailed views
@router.get("/resources/compute", response_model=List[schemas.Resource])
async def get_compute_resources(db: Session = Depends(get_db)):
    """Get only compute instances"""
    filters = schemas.ResourceFilter(resource_type="Compute Instance")
    return crud.get_resources(db, filters=filters)


@router.get("/resources/oke", response_model=List[schemas.Resource])
async def get_oke_resources(db: Session = Depends(get_db)):
    """Get OKE clusters and node pools"""
    resources = []
    oke_filters = schemas.ResourceFilter(resource_type="OKE Cluster")
    nodepool_filters = schemas.ResourceFilter(resource_type="OKE Node Pool")
    
    resources.extend(crud.get_resources(db, filters=oke_filters))
    resources.extend(crud.get_resources(db, filters=nodepool_filters))
    
    return resources


@router.get("/resources/databases", response_model=List[schemas.Resource])
async def get_database_resources(db: Session = Depends(get_db)):
    """Get database resources"""
    resources = []
    mysql_filters = schemas.ResourceFilter(resource_type="MySQL DB System")
    heatwave_filters = schemas.ResourceFilter(resource_type="MySQL HeatWave DB")
    
    resources.extend(crud.get_resources(db, filters=mysql_filters))
    resources.extend(crud.get_resources(db, filters=heatwave_filters))
    
    return resources


@router.get("/resources/load-balancers", response_model=List[schemas.Resource])
async def get_load_balancer_resources(db: Session = Depends(get_db)):
    """Get load balancer resources"""
    filters = schemas.ResourceFilter(resource_type="Load Balancer")
    return crud.get_resources(db, filters=filters)
