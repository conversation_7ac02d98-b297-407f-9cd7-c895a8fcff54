"""
Celery configuration for background tasks
"""
from celery import Celery
from app.config import settings

# Create Celery instance
celery_app = Celery(
    "oci_inventory",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["app.tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks configuration
celery_app.conf.beat_schedule = {
    "auto-inventory-scan": {
        "task": "app.tasks.run_scheduled_inventory_scan",
        "schedule": settings.auto_refresh_interval_minutes * 60.0,  # Convert to seconds
    },
}

if __name__ == "__main__":
    celery_app.start()
