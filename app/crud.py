"""
CRUD operations for database models
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, InstrumentedAttribute
from sqlalchemy import func, and_, or_, ColumnElement
from app import models, schemas
import json

from app.models import Resource, Compartment, Region, NetworkTopology


def create_compartment(db: Session, compartment: schemas.CompartmentCreate) -> models.Compartment:
    """Create or update a compartment"""
    db_compartment = db.query(models.Compartment).filter(models.Compartment.id == compartment.id).first()
    if db_compartment:
        # Update existing
        for key, value in compartment.dict().items():
            setattr(db_compartment, key, value)
    else:
        # Create new
        db_compartment = models.Compartment(**compartment.dict())
        db.add(db_compartment)
    db.commit()
    db.refresh(db_compartment)
    return db_compartment


def create_region(db: Session, region: schemas.RegionCreate) -> models.Region:
    """Create or update a region"""
    db_region = db.query(models.Region).filter(models.Region.name == region.name).first()
    if db_region:
        # Update existing
        for key, value in region.dict().items():
            setattr(db_region, key, value)
    else:
        # Create new
        db_region = models.Region(**region.dict())
        db.add(db_region)
    db.commit()
    db.refresh(db_region)
    return db_region


def create_resource(db: Session, resource: schemas.ResourceCreate) -> models.Resource:
    """Create or update a resource"""
    db_resource = db.query(models.Resource).filter(models.Resource.oci_id == resource.oci_id).first()
    if db_resource:
        # Update existing
        for key, value in resource.dict().items():
            setattr(db_resource, key, value)
    else:
        # Create new
        db_resource = models.Resource(**resource.dict())
        db.add(db_resource)
    db.commit()
    db.refresh(db_resource)
    return db_resource


def get_resources_with_compartment_names(
    db: Session,
    skip: int = 0,
    limit: int = 1000,
    filters: Optional[schemas.ResourceFilter] = None
) -> List[Dict]:
    """Get resources with compartment names included"""
    # Build base query with join to get compartment names
    query = db.query(
        models.Resource,
        models.Compartment.name.label('compartment_name')
    ).join(
        models.Compartment,
        models.Resource.compartment_id == models.Compartment.id
    )

    if filters:
        if filters.compartment_id:
            query = query.filter(models.Resource.compartment_id == filters.compartment_id)
        if filters.compartment_name:
            query = query.filter(models.Compartment.name == filters.compartment_name)
        if filters.region_name:
            query = query.filter(models.Resource.region_name == filters.region_name)
        if filters.environment:
            query = query.filter(models.Resource.environment == filters.environment)
        if filters.resource_type:
            query = query.filter(models.Resource.resource_type == filters.resource_type)
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    models.Resource.name.ilike(search_term),
                    models.Resource.resource_type.ilike(search_term),
                    models.Resource.environment.ilike(search_term),
                    models.Resource.purpose.ilike(search_term),
                    models.Compartment.name.ilike(search_term)
                )
            )

    results = query.offset(skip).limit(limit).all()

    # Convert to list of dicts with compartment_name included
    resources_with_compartments = []
    for resource, compartment_name in results:
        resource_dict = {
            "id": resource.id,
            "oci_id": resource.oci_id,
            "name": resource.name,
            "resource_type": resource.resource_type,
            "compartment_id": resource.compartment_id,
            "compartment_name": compartment_name,
            "region_name": resource.region_name,
            "vcn_name": resource.vcn_name,
            "subnet_name": resource.subnet_name,
            "private_ip": resource.private_ip,
            "public_ip": resource.public_ip,
            "shape": resource.shape,
            "node_count": resource.node_count,
            "parent_cluster": resource.parent_cluster,
            "cidr_block": resource.cidr_block,
            "environment": resource.environment,
            "purpose": resource.purpose,
            "tags": resource.tags,
            "lifecycle_state": resource.lifecycle_state,
            "time_created": resource.time_created,
            "last_updated": resource.last_updated,
            "estimated_monthly_cost": resource.estimated_monthly_cost
        }
        resources_with_compartments.append(resource_dict)

    return resources_with_compartments

def get_resources(
    db: Session,
    skip: int = 0,
    limit: int = 1000,
    filters: Optional[schemas.ResourceFilter] = None
) -> List[models.Resource]:
    """Get resources with optional filtering (legacy function)"""
    query = db.query(models.Resource)

    if filters:
        if filters.compartment_id:
            query = query.filter(models.Resource.compartment_id == filters.compartment_id)
        if filters.compartment_name:
            # Filter by compartment name - need to join with compartments table
            query = query.join(models.Compartment, models.Resource.compartment_id == models.Compartment.id)
            query = query.filter(models.Compartment.name == filters.compartment_name)
        if filters.region_name:
            query = query.filter(models.Resource.region_name == filters.region_name)
        if filters.environment:
            query = query.filter(models.Resource.environment == filters.environment)
        if filters.resource_type:
            query = query.filter(models.Resource.resource_type == filters.resource_type)
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    models.Resource.name.ilike(search_term),
                    models.Resource.resource_type.ilike(search_term),
                    models.Resource.environment.ilike(search_term),
                    models.Resource.purpose.ilike(search_term)
                )
            )

    return query.offset(skip).limit(limit).all()


def get_compartments(db: Session) -> list[type[Compartment]]:
    """Get all compartments"""
    return db.query(models.Compartment).all()


def get_regions(db: Session) -> list[type[Region]]:
    """Get all regions"""
    return db.query(models.Region).all()


def get_dashboard_stats(db: Session) -> schemas.DashboardStats:
    """Get dashboard statistics"""
    total_resources = db.query(models.Resource).count()
    total_compartments = db.query(models.Compartment).count()
    total_regions = db.query(models.Region).count()
    
    # Resources by type
    resources_by_type = dict(
        db.query(models.Resource.resource_type, func.count(models.Resource.id))
        .group_by(models.Resource.resource_type)
        .all()
    )
    
    # Resources by environment
    resources_by_environment = dict(
        db.query(models.Resource.environment, func.count(models.Resource.id))
        .group_by(models.Resource.environment)
        .all()
    )
    
    # Resources by region
    resources_by_region = dict(
        db.query(models.Resource.region_name, func.count(models.Resource.id))
        .group_by(models.Resource.region_name)
        .all()
    )

    # Resources by compartment (using compartment names)
    resources_by_compartment = {}
    compartment_map = {c.id: c.name for c in db.query(models.Compartment).all()}
    compartment_counts = dict(
        db.query(models.Resource.compartment_id, func.count(models.Resource.id))
        .group_by(models.Resource.compartment_id)
        .all()
    )
    for comp_id, count in compartment_counts.items():
        comp_name = compartment_map.get(comp_id, f"Unknown ({comp_id})")
        resources_by_compartment[comp_name] = count
    
    # Last scan
    last_scan_record = db.query(models.InventoryScan).filter(
        models.InventoryScan.status == "completed"
    ).order_by(models.InventoryScan.scan_completed.desc()).first()
    
    last_scan = last_scan_record.scan_completed if last_scan_record else None
    
    return schemas.DashboardStats(
        total_resources=total_resources,
        total_compartments=total_compartments,
        total_regions=total_regions,
        resources_by_type=resources_by_type,
        resources_by_environment=resources_by_environment,
        resources_by_region=resources_by_region,
        resources_by_compartment=resources_by_compartment,
        last_scan=last_scan
    )


def create_inventory_scan(db: Session, scan: schemas.InventoryScanCreate) -> models.InventoryScan:
    """Create a new inventory scan record"""
    db_scan = models.InventoryScan(**scan.dict())
    db.add(db_scan)
    db.commit()
    db.refresh(db_scan)
    return db_scan


def update_inventory_scan(db: Session, scan_id: int, **kwargs) -> Optional[models.InventoryScan]:
    """Update an inventory scan record"""
    db_scan = db.query(models.InventoryScan).filter(models.InventoryScan.id == scan_id).first()
    if db_scan:
        for key, value in kwargs.items():
            setattr(db_scan, key, value)
        db.commit()
        db.refresh(db_scan)
    return db_scan


def get_latest_scan(db: Session) -> Optional[models.InventoryScan]:
    """Get the latest inventory scan"""
    return db.query(models.InventoryScan).order_by(models.InventoryScan.scan_started.desc()).first()


def clear_all_resources(db: Session):
    """Clear all resources (for fresh scan)"""
    db.query(models.Resource).delete()
    db.query(models.NetworkTopology).delete()
    db.commit()


def create_network_topology(db: Session, network: schemas.NetworkTopologyCreate) -> models.NetworkTopology:
    """Create or update network topology"""
    db_network = db.query(models.NetworkTopology).filter(models.NetworkTopology.oci_id == network.oci_id).first()
    if db_network:
        # Update existing
        for key, value in network.dict().items():
            setattr(db_network, key, value)
    else:
        # Create new
        db_network = models.NetworkTopology(**network.dict())
        db.add(db_network)
    db.commit()
    db.refresh(db_network)
    return db_network


def get_network_topology_with_compartment_names(db: Session) -> List[Dict]:
    """Get network topology data with compartment names included"""
    # Build query with join to get compartment names
    query = db.query(
        models.NetworkTopology,
        models.Compartment.name.label('compartment_name')
    ).join(
        models.Compartment,
        models.NetworkTopology.compartment_id == models.Compartment.id
    )

    results = query.all()

    # Convert to list of dicts with compartment_name included
    topology_with_compartments = []
    for network, compartment_name in results:
        network_dict = {
            "id": network.id,
            "oci_id": network.oci_id,
            "name": network.name,
            "resource_type": network.resource_type,
            "compartment_id": network.compartment_id,
            "compartment_name": compartment_name,
            "region_name": network.region_name,
            "cidr_block": network.cidr_block,
            "vcn_name": network.vcn_name,
            "environment": network.environment,
            "purpose": network.purpose,
            "tags": network.tags,
            "time_created": network.time_created,
            "last_updated": network.last_updated
        }
        topology_with_compartments.append(network_dict)

    return topology_with_compartments

def get_network_topology(db: Session) -> list[type[NetworkTopology]]:
    """Get all network topology data (legacy function)"""
    return db.query(models.NetworkTopology).all()


def get_unique_values(db: Session, field: str) -> list[InstrumentedAttribute] | list[ColumnElement[Any]] | list[Any]:
    """Get unique values for a specific field"""
    if field == "compartment_name":
        return [c.name for c in db.query(models.Compartment).all()]
    elif field == "region_name":
        return [r.name for r in db.query(models.Region).all()]
    elif field == "environment":
        return [env[0] for env in db.query(models.Resource.environment).distinct().all() if env[0] and env[0] != "N/A"]
    elif field == "resource_type":
        return [rt[0] for rt in db.query(models.Resource.resource_type).distinct().all() if rt[0]]
    else:
        return []
