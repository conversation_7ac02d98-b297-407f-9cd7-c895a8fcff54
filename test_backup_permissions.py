#!/usr/bin/env python3
"""
Test script to check if the service account has backup policy permissions
"""

import oci
from oci.config import from_file

def test_backup_permissions(config_profile="INVENTORY_READONLY"):
    """Test if the service account can read backup policies"""
    
    try:
        # Load OCI config
        config = from_file(profile_name=config_profile)
        tenancy_id = config["tenancy"]
        
        # Initialize blockstorage client
        blockstorage_client = oci.core.BlockstorageClient(config)
        
        print(f"🔍 Testing backup policy permissions for profile: {config_profile}")
        print(f"🏢 Tenancy: {tenancy_id}")
        
        # Test 1: Try to list volume backup policies
        print("\n📋 Test 1: Listing volume backup policies...")
        try:
            policies = oci.pagination.list_call_get_all_results(
                blockstorage_client.list_volume_backup_policies,
                compartment_id=tenancy_id
            ).data
            
            print(f"✅ SUCCESS: Found {len(policies)} backup policies")
            if policies:
                print("   Sample policies:")
                for i, policy in enumerate(policies[:3]):  # Show first 3
                    print(f"   - {policy.display_name} ({policy.id})")
                    if i >= 2:  # Limit to 3 policies
                        break
            
            return True
            
        except oci.exceptions.ServiceError as e:
            if hasattr(e, 'status'):
                if e.status in [401, 403]:
                    print(f"❌ PERMISSION DENIED: {e}")
                    print("   Your service account needs these IAM permissions:")
                    print("   - allow group <group> to read volume-backup-policies in tenancy")
                    print("   - allow group <group> to read volume-backup-policy-assignments in tenancy")
                elif e.status == 404:
                    print("❌ NOT FOUND: No backup policies found or wrong compartment")
                else:
                    print(f"❌ ERROR ({e.status}): {e}")
            else:
                print(f"❌ ERROR: {e}")
            return False
            
        except Exception as e:
            print(f"❌ UNEXPECTED ERROR: {e}")
            return False
            
    except Exception as e:
        print(f"❌ FAILED TO INITIALIZE: {e}")
        return False

def test_basic_permissions(config_profile="INVENTORY_READONLY"):
    """Test basic permissions that should work for inventory"""
    
    try:
        config = from_file(profile_name=config_profile)
        tenancy_id = config["tenancy"]
        
        # Test basic compute permissions
        compute_client = oci.core.ComputeClient(config)
        identity_client = oci.identity.IdentityClient(config)
        
        print(f"\n🔍 Testing basic inventory permissions...")
        
        # Test compartment access
        try:
            compartments = oci.pagination.list_call_get_all_results(
                identity_client.list_compartments,
                tenancy_id,
                compartment_id_in_subtree=True,
                access_level="ANY"
            ).data
            print(f"✅ Compartments: Can read {len(compartments)} compartments")
        except Exception as e:
            print(f"❌ Compartments: {e}")
        
        # Test instance access (in root compartment)
        try:
            instances = oci.pagination.list_call_get_all_results(
                compute_client.list_instances,
                compartment_id=tenancy_id
            ).data
            print(f"✅ Instances: Can read {len(instances)} instances in root compartment")
        except Exception as e:
            print(f"❌ Instances: {e}")
            
    except Exception as e:
        print(f"❌ Basic permissions test failed: {e}")

if __name__ == "__main__":
    print("🧪 OCI Backup Policy Permissions Test")
    print("=" * 50)
    
    # Test basic permissions first
    test_basic_permissions()
    
    # Test backup permissions
    has_backup_perms = test_backup_permissions()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    if has_backup_perms:
        print("✅ Your service account HAS backup policy permissions")
        print("   You can enable backup status checking in the inventory script")
        print("   Set ENABLE_BACKUP_STATUS_CHECK = True")
    else:
        print("❌ Your service account LACKS backup policy permissions")
        print("   Options:")
        print("   1. Add IAM permissions for backup policies (recommended)")
        print("   2. Set ENABLE_BACKUP_STATUS_CHECK = False to skip backup checks")
        print("\n   Required IAM policy statements:")
        print("   allow group <your-group> to read volume-backup-policies in tenancy")
        print("   allow group <your-group> to read volume-backup-policy-assignments in tenancy")
