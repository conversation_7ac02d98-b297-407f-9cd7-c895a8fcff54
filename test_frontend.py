#!/usr/bin/env python3
"""
Test script to verify the frontend setup
"""
import subprocess
import sys
import os
from pathlib import Path
import json

def test_node_npm():
    """Test Node.js and npm installation"""
    print("🧪 Testing Node.js and npm...")
    
    try:
        # Test Node.js
        node_result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if node_result.returncode == 0:
            print(f"✅ Node.js version: {node_result.stdout.strip()}")
        else:
            print("❌ Node.js not found")
            return False
        
        # Test npm
        npm_result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if npm_result.returncode == 0:
            print(f"✅ npm version: {npm_result.stdout.strip()}")
        else:
            print("❌ npm not found")
            return False
        
        return True
    except FileNotFoundError:
        print("❌ Node.js or npm not found")
        return False

def test_frontend_structure():
    """Test frontend directory structure"""
    print("📁 Testing frontend structure...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    required_files = [
        "package.json",
        "src/App.js",
        "src/index.js",
        "src/components/Dashboard.js",
        "src/components/ResourceTable.js",
        "src/components/NetworkTopology.js",
        "public/index.html",
        "tailwind.config.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = frontend_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files present")
    return True

def test_package_json():
    """Test package.json configuration"""
    print("📦 Testing package.json...")
    
    package_json_path = Path("frontend/package.json")
    if not package_json_path.exists():
        print("❌ package.json not found")
        return False
    
    try:
        with open(package_json_path, 'r') as f:
            package_data = json.load(f)
        
        # Check required dependencies
        required_deps = [
            'react',
            'react-dom',
            'axios',
            'chart.js',
            'react-chartjs-2',
            'tailwindcss'
        ]
        
        dependencies = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
        missing_deps = [dep for dep in required_deps if dep not in dependencies]
        
        if missing_deps:
            print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
            return False
        
        print("✅ All required dependencies present")
        return True
        
    except json.JSONDecodeError:
        print("❌ Invalid package.json format")
        return False
    except Exception as e:
        print(f"❌ Error reading package.json: {e}")
        return False

def test_tailwind_config():
    """Test Tailwind CSS configuration"""
    print("🎨 Testing Tailwind configuration...")
    
    tailwind_config = Path("frontend/tailwind.config.js")
    if not tailwind_config.exists():
        print("❌ tailwind.config.js not found")
        return False
    
    try:
        with open(tailwind_config, 'r') as f:
            content = f.read()
        
        # Check for basic configuration
        if 'content' in content and 'src/**/*.{js,jsx,ts,tsx}' in content:
            print("✅ Tailwind configuration looks good")
            return True
        else:
            print("❌ Tailwind configuration incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Error reading Tailwind config: {e}")
        return False

def test_component_imports():
    """Test that components can be imported"""
    print("⚛️ Testing component structure...")
    
    components_dir = Path("frontend/src/components")
    if not components_dir.exists():
        print("❌ Components directory not found")
        return False
    
    required_components = [
        "Dashboard.js",
        "ResourceTable.js", 
        "NetworkTopology.js",
        "LoadingSpinner.js",
        "ErrorMessage.js"
    ]
    
    missing_components = []
    for component in required_components:
        component_path = components_dir / component
        if not component_path.exists():
            missing_components.append(component)
        else:
            # Check if component has basic React structure
            try:
                with open(component_path, 'r') as f:
                    content = f.read()
                if 'import React' not in content or 'export default' not in content:
                    missing_components.append(f"{component} (invalid structure)")
            except Exception:
                missing_components.append(f"{component} (read error)")
    
    if missing_components:
        print(f"❌ Component issues: {', '.join(missing_components)}")
        return False
    
    print("✅ All components present and valid")
    return True

def test_backend_connection():
    """Test if backend is accessible"""
    print("🔗 Testing backend connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is accessible")
            return True
        else:
            print(f"⚠️ Backend returned status {response.status_code}")
            return False
    except ImportError:
        print("⚠️ requests library not available, skipping backend test")
        return True
    except Exception as e:
        print(f"⚠️ Backend not accessible: {e}")
        print("   Make sure to run 'python run_dev.py' first")
        return False

def main():
    """Run all frontend tests"""
    print("🎨 OCI Inventory Dashboard Frontend Test")
    print("=" * 50)
    
    tests = [
        ("Node.js and npm", test_node_npm),
        ("Frontend Structure", test_frontend_structure),
        ("Package Configuration", test_package_json),
        ("Tailwind Configuration", test_tailwind_config),
        ("Component Structure", test_component_imports),
        ("Backend Connection", test_backend_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Frontend is ready.")
        print("\nNext steps:")
        print("1. Install dependencies: cd frontend && npm install")
        print("2. Start frontend: python start_frontend.py")
        print("3. Open: http://localhost:3000")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please fix the issues above.")
        
        if not any(name == "Node.js and npm" and result for name, result in results):
            print("\n💡 Install Node.js first:")
            print("   https://nodejs.org/")
        
        sys.exit(1)

if __name__ == "__main__":
    main()
