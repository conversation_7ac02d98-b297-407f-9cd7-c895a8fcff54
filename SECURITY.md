# 🔐 Security Guidelines

## 🚨 **NEVER COMMIT THESE FILES**

### **Credentials & Keys**
- ❌ `.env` files (use `.env.example` instead)
- ❌ OCI private keys (`.pem`, `.key` files)
- ❌ OCI config files
- ❌ API keys or tokens
- ❌ Database files (`*.db`, `*.sqlite`)

### **Sensitive Data**
- ❌ Export files with real data (`*.xlsx`, `*.csv`)
- ❌ Log files that might contain sensitive info
- ❌ Backup files with real configurations

## ✅ **Safe to Commit**

### **Configuration Templates**
- ✅ `.env.example` (with placeholder values)
- ✅ `config.example.py` (with sample settings)
- ✅ Documentation and README files
- ✅ Source code (without hardcoded secrets)

## 🛡️ **Security Best Practices**

### **1. Environment Variables**
Always use environment variables for sensitive data:

```bash
# ❌ DON'T DO THIS in code
oci_user = "ocid1.user.oc1..aaaaaaaa..."

# ✅ DO THIS instead
oci_user = os.getenv("OCI_USER_OCID")
```

### **2. OCI Configuration**
- Store OCI config in `~/.oci/config` (outside project directory)
- Use read-only service accounts
- Rotate API keys regularly (every 90 days)

### **3. Database Security**
- SQLite database files are ignored by `.gitignore`
- Use environment variables for database URLs
- Never commit databases with real data

### **4. API Keys**
- Generate separate API keys for different environments
- Use descriptive names for API keys
- Monitor API key usage in OCI console

## 🔍 **Pre-Commit Checklist**

Before committing code, verify:

- [ ] No `.env` files in the commit
- [ ] No OCI private keys or config files
- [ ] No database files with real data
- [ ] No export files with sensitive information
- [ ] No hardcoded credentials in source code
- [ ] All sensitive values use environment variables

## 🚨 **If You Accidentally Commit Secrets**

### **Immediate Actions:**
1. **Rotate the compromised credentials immediately**
2. **Remove the file from git history:**
   ```bash
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch path/to/sensitive/file' \
   --prune-empty --tag-name-filter cat -- --all
   ```
3. **Force push to overwrite history:**
   ```bash
   git push origin --force --all
   ```
4. **Notify team members to re-clone the repository**

### **OCI-Specific Actions:**
1. **Delete the compromised API key** in OCI console
2. **Generate new API keys**
3. **Update all environments** with new keys
4. **Review audit logs** for any unauthorized access

## 🔐 **Recommended OCI Security Setup**

### **Service Account Configuration:**
```ini
# ~/.oci/config
[INVENTORY_READONLY]
user=ocid1.user.oc1..aaaaaaaa[REDACTED]
fingerprint=xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx:xx
key_file=~/.oci/oci_inventory_readonly.pem
tenancy=ocid1.tenancy.oc1..aaaaaaaa[REDACTED]
region=us-phoenix-1
```

### **Environment Variables:**
```bash
# .env (never commit this file)
OCI_CONFIG_PROFILE=INVENTORY_READONLY
DATABASE_URL=sqlite:///./oci_inventory.db
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key-here
```

### **Group-Based Permissions:**
- Create `oci-inventory-readonly-group`
- Assign read-only policies to the group
- Add service users to the group
- Never assign policies directly to users

## 📊 **Monitoring & Auditing**

### **Regular Security Reviews:**
- [ ] Review OCI audit logs monthly
- [ ] Rotate API keys quarterly
- [ ] Review group memberships quarterly
- [ ] Check for unused service accounts

### **Automated Monitoring:**
- Set up OCI alerts for unusual API activity
- Monitor failed authentication attempts
- Track resource access patterns

## 🆘 **Security Incident Response**

### **If You Suspect a Breach:**
1. **Immediately rotate all API keys**
2. **Review OCI audit logs** for unauthorized activity
3. **Check application logs** for suspicious requests
4. **Notify security team** if in enterprise environment
5. **Document the incident** and lessons learned

## 📞 **Getting Help**

### **OCI Security Resources:**
- [OCI Security Best Practices](https://docs.oracle.com/en-us/iaas/Content/Security/Concepts/security_guide.htm)
- [OCI Identity and Access Management](https://docs.oracle.com/en-us/iaas/Content/Identity/Concepts/overview.htm)
- [OCI Audit Service](https://docs.oracle.com/en-us/iaas/Content/Audit/Concepts/auditoverview.htm)

### **Emergency Contacts:**
- OCI Support: [Oracle Cloud Support](https://support.oracle.com/)
- Security Team: [Your organization's security contact]

---

**Remember: Security is everyone's responsibility!** 🛡️

When in doubt, ask for a security review before committing or deploying.
