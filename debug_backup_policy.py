#!/usr/bin/env python3
"""
Debug script to test backup policy retrieval for a specific instance
"""

import oci
import logging
from oci.config import from_file

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_backup_policy(instance_id, compartment_id, config_profile="INVENTORY_READONLY"):
    """Debug backup policy for a specific instance"""
    
    try:
        # Load OCI config
        config = from_file(profile_name=config_profile)
        
        # Initialize clients
        compute_client = oci.core.ComputeClient(config)
        blockstorage_client = oci.core.BlockstorageClient(config)
        
        print(f"🔍 Debugging backup policy for instance: {instance_id}")
        print(f"📁 Compartment: {compartment_id}")
        
        # Step 1: Get instance details first to get the availability domain
        print("\n📋 Step 1: Getting instance details...")
        try:
            instance = compute_client.get_instance(instance_id=instance_id).data
            availability_domain = instance.availability_domain
            print(f"✅ Instance found in AD: {availability_domain}")
        except Exception as e:
            print(f"❌ Error getting instance details: {e}")
            return

        # Step 2: Get boot volume attachments
        print("\n📋 Step 2: Getting boot volume attachments...")
        boot_volume_attachments = oci.pagination.list_call_get_all_results(
            compute_client.list_boot_volume_attachments,
            availability_domain=availability_domain,
            compartment_id=compartment_id,
            instance_id=instance_id
        ).data
        
        if not boot_volume_attachments:
            print("❌ No boot volume attachments found")
            return
        
        boot_volume_id = boot_volume_attachments[0].boot_volume_id
        print(f"✅ Found boot volume: {boot_volume_id}")

        # Step 3: Get boot volume details
        print("\n📋 Step 3: Getting boot volume details...")
        try:
            boot_volume = blockstorage_client.get_boot_volume(boot_volume_id=boot_volume_id).data
            print(f"✅ Boot volume name: {boot_volume.display_name}")
            print(f"✅ Boot volume state: {boot_volume.lifecycle_state}")
        except Exception as e:
            print(f"❌ Error getting boot volume details: {e}")

        # Step 4: Try to get backup policy assignment
        print("\n📋 Step 4: Checking backup policy assignment...")
        try:
            policy_assignment = blockstorage_client.get_volume_backup_policy_asset_assignment(
                asset_id=boot_volume_id
            ).data

            print(f"🔍 Policy assignment object: {policy_assignment}")
            print(f"🔍 Policy assignment type: {type(policy_assignment)}")

            if policy_assignment:
                print("✅ Policy assignment object exists")

                # Check if it's a list (which it appears to be)
                if isinstance(policy_assignment, list):
                    print(f"🔍 It's a list with {len(policy_assignment)} items")

                    if len(policy_assignment) > 0:
                        print("✅ Found policy assignments in the list")

                        for i, assignment in enumerate(policy_assignment):
                            print(f"\n🔍 Assignment {i+1}:")
                            print(f"   Type: {type(assignment)}")
                            print(f"   Available attributes: {[attr for attr in dir(assignment) if not attr.startswith('_')]}")

                            # Check all possible policy ID attributes
                            policy_id = None
                            for attr in ['policy_id', 'volume_backup_policy_id', 'backup_policy_id']:
                                if hasattr(assignment, attr):
                                    value = getattr(assignment, attr)
                                    print(f"   🔍 {attr}: {value}")
                                    if value:
                                        policy_id = value

                            # Show all non-callable attributes
                            for attr in dir(assignment):
                                if not attr.startswith('_'):
                                    try:
                                        value = getattr(assignment, attr)
                                        if not callable(value):
                                            print(f"   🔍 {attr}: {value}")
                                    except:
                                        pass

                            if policy_id:
                                print(f"✅ Found policy ID in assignment {i+1}: {policy_id}")
                                break
                    else:
                        print("❌ List is empty - no policy assignments")
                        policy_id = None
                else:
                    print(f"🔍 Available attributes: {dir(policy_assignment)}")

                    # Check all possible policy ID attributes
                    policy_id = None
                    for attr in ['policy_id', 'volume_backup_policy_id', 'backup_policy_id']:
                        if hasattr(policy_assignment, attr):
                            value = getattr(policy_assignment, attr)
                            print(f"🔍 {attr}: {value}")
                            if value:
                                policy_id = value

                if policy_id:
                    print(f"✅ Found policy ID: {policy_id}")

                    # Step 5: Get policy details
                    print("\n📋 Step 5: Getting policy details...")
                    try:
                        policy = blockstorage_client.get_volume_backup_policy(
                            policy_id=policy_id
                        ).data

                        print(f"✅ Policy name: {policy.display_name}")
                        print(f"✅ Policy compartment: {policy.compartment_id}")

                        if hasattr(policy, 'schedules') and policy.schedules:
                            print(f"✅ Number of schedules: {len(policy.schedules)}")
                            for i, schedule in enumerate(policy.schedules):
                                print(f"   Schedule {i+1}:")
                                print(f"     - Backup type: {schedule.backup_type}")
                                print(f"     - Period: {schedule.period}")
                                if hasattr(schedule, 'retention_seconds') and schedule.retention_seconds:
                                    retention_days = schedule.retention_seconds // (24 * 3600)
                                    print(f"     - Retention: {retention_days} days")
                        else:
                            print("⚠️  No schedules found in policy")

                    except Exception as e:
                        print(f"❌ Error getting policy details: {e}")
                else:
                    print("❌ No policy_id found in assignment object")

                # Also check if there are other useful attributes
                for attr in dir(policy_assignment):
                    if not attr.startswith('_') and attr not in ['policy_id', 'volume_backup_policy_id', 'backup_policy_id']:
                        try:
                            value = getattr(policy_assignment, attr)
                            if not callable(value):
                                print(f"🔍 {attr}: {value}")
                        except:
                            pass
            else:
                print("❌ Policy assignment object is None")

        except oci.exceptions.ServiceError as e:
            if hasattr(e, 'status') and e.status == 404:
                print("❌ No backup policy assigned (404 Not Found)")
            else:
                print(f"❌ Error getting backup policy assignment: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


            
    except Exception as e:
        print(f"❌ Failed to initialize: {e}")

if __name__ == "__main__":
    # You can modify these values to test with your specific instance
    instance_id = input("Enter instance OCID: ").strip()
    compartment_id = input("Enter compartment OCID: ").strip()
    
    if instance_id and compartment_id:
        debug_backup_policy(instance_id, compartment_id)
    else:
        print("❌ Please provide both instance OCID and compartment OCID")
